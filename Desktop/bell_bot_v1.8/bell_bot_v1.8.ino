#include "main.h"
volatile bool configModeTimeout = false;
// With these:
WebServer configServer(80);   // Configuration web server on port 8080
WebServer runtimeServer(80);  // Runtime web server on port 80
// #define CONFIG_SERVER_PORT 8080
// #define RUNTIME_SERVER_PORT 80
// #define CONFIG_MODE_TIMEOUT_MS (3 * 60 * 1000) // 3 minutes
// Preferences and Timer
Preferences preferences;
TimerHandle_t apModeTimer;

// Network Status
bool eth_connected = false;
bool wifi_sta_connected = false;
NetworkConfig config;

// Server Status Flags
bool configServerStarted = false;
bool runtimeServerStarted = false;
bool firstBootAfterPowerOn = true;

// Task Handles
TaskHandle_t webServerTaskHandle = NULL;
TaskHandle_t netMonitorTaskHandle = NULL;

// MAC Address
uint8_t eth_mac[6] = { 0xDE, 0xAD, 0xBE, 0xEF, 0xFE, 0xED };

// BellBot Hardware
//TFT_eSPI tft = TFT_eSPI();
PCF8574 pcf(0x20);
RTC_DS3231 rtc;

// WiFi Settings
const char* ap_ssid = "BOT_AP";
const char* ap_password = "123456789";
char sta_ssid[32] = "";
char sta_password[64] = "";
bool wifi_sta_mode = false;
IPAddress local_ip(192, 168, 4, 1);
IPAddress gateway(192, 168, 4, 1);
IPAddress subnet(255, 255, 255, 0);

// Authentication
const char* auth_username = "admin";
const char* auth_password = "admin";
bool auth_enabled = true;

// NTP Settings
WiFiUDP ntpUDP;
NTPClient timeClient(ntpUDP);
String ntpServer = "pool.ntp.org";
unsigned long lastNtpUpdate = 0;
const unsigned long ntpUpdateInterval = 60000;
bool ntpEnabled = false;

// Alarm Control
bool alarmActive = false;
unsigned long alarmStartTime = 0;
unsigned long alarmDuration = 0;
String lastTriggeredAlarmTime = "";
// Add these global variables
bool rtcInitialized = false;
unsigned long lastDisplayUpdate = 0;
const unsigned long DISPLAY_UPDATE_INTERVAL = 1000;  // Update display every 1 second

// Display Control
bool displayBlinkState = true;
unsigned long lastBlinkTime = 0;
const unsigned long blinkInterval = 50;

// Session Management
unsigned long lastActivityTime = 0;
const unsigned long SESSION_TIMEOUT = 60000;
String currentSessionId = "";

// Schedule Data
DaySchedule weekSchedule[7];
DynamicJsonDocument holidays(2048);
String currentView = "main";
String selectedDay = "";
String selectedHoliday = "";

// OTA Update
bool otaUpdating = false;
unsigned long otaStartTime = 0;

// ==================== NETWORK FUNCTIONS ====================

void loadConfiguration() {
  config.wifiEnabled = preferences.getBool("wifiEnabled", false);
  size_t len = preferences.getString("wifiSSID", config.wifiSSID, sizeof(config.wifiSSID));
  if (len == 0) strcpy(config.wifiSSID, "");
  len = preferences.getString("wifiPassword", config.wifiPassword, sizeof(config.wifiPassword));
  if (len == 0) strcpy(config.wifiPassword, "");

  config.ethernetEnabled = preferences.getBool("ethernetEnabled", false);
  config.useDHCP = preferences.getBool("useDHCP", true);

  String ipStr = preferences.getString("staticIP", "");
  config.staticIP.fromString(ipStr.length() ? ipStr : "***********00");
  ipStr = preferences.getString("subnet", "");
  config.subnet.fromString(ipStr.length() ? ipStr : "*************");
  ipStr = preferences.getString("gateway", "");
  config.gateway.fromString(ipStr.length() ? ipStr : "***********");
}

void saveConfiguration() {
  if (!config.useDHCP) {
    if (config.staticIP == INADDR_NONE || config.subnet == INADDR_NONE || config.gateway == INADDR_NONE) {
      Serial.println("Invalid static IP configuration!");
      return;
    }
  }

  preferences.putBool("wifiEnabled", config.wifiEnabled);
  preferences.putString("wifiSSID", config.wifiSSID);
  preferences.putString("wifiPassword", config.wifiPassword);
  preferences.putBool("ethernetEnabled", config.ethernetEnabled);
  preferences.putBool("useDHCP", config.useDHCP);
  preferences.putString("staticIP", config.staticIP.toString());
  preferences.putString("subnet", config.subnet.toString());
  preferences.putString("gateway", config.gateway.toString());
  preferences.putBool("configured", true);

  Serial.println("Configuration saved");
}

void startConfigMode() {
  Serial.println("Starting configuration mode");

  // Stop any existing network interfaces
  WiFi.disconnect(true);
  ETH.end();
  delay(1000);

  // Start AP mode
  WiFi.mode(WIFI_AP);
  WiFi.softAPConfig(local_ip, gateway, subnet);
  WiFi.softAP(ap_ssid, ap_password);

  Serial.print("AP IP: ");
  Serial.println(WiFi.softAPIP());

  // Setup config web server
  setupConfigWebServer();
  configServer.begin();
  configServerStarted = true;
  runtimeServerStarted = false;

  // Start config mode timer
  // Start config mode timer
  apModeTimer = xTimerCreate(
    "APModeTimer",
    pdMS_TO_TICKS(CONFIG_MODE_TIMEOUT_MS),
    pdFALSE,                   // One-shot timer
    (void*)0,                  // Timer ID
    configModeTimeoutCallback  // Just the callback function
  );

  if (apModeTimer == NULL) {
    Serial.println("Failed to create AP mode timer");
  } else {
    xTimerStart(apModeTimer, 0);
  }

  if (apModeTimer != NULL) {
    xTimerStart(apModeTimer, 0);
  } else {
    Serial.println("Failed to create config mode timer");
  }
}

void setDefaultConfiguration() {
  config.wifiEnabled = true;
  strcpy(config.wifiSSID, "default-ssid");
  strcpy(config.wifiPassword, "default-password");
  config.ethernetEnabled = false;
  config.useDHCP = true;
  config.staticIP.fromString("***********00");
  config.subnet.fromString("*************");
  config.gateway.fromString("***********");

  saveConfiguration();
}

bool shouldStartInConfigMode() {
  if (firstBootAfterPowerOn) {
    firstBootAfterPowerOn = false;
    return true;
  }

  if (!preferences.getBool("configured", false)) {
    return true;
  }

  size_t len = preferences.getString("wifiSSID", config.wifiSSID, sizeof(config.wifiSSID));
  if (config.wifiEnabled && len == 0) {
    return true;
  }
  return false;
}

bool startRuntimeServer() {
  if (!setupRuntimeWebServer()) {
    Serial.println("Failed to setup runtime server");
    return false;
  }

  runtimeServer.begin();
  runtimeServerStarted = true;
  configServerStarted = false;

  Serial.print("Runtime server started on ");
  if (eth_connected) {
    Serial.println(ETH.localIP());
  } else if (wifi_sta_connected) {
    Serial.println(WiFi.localIP());
  }

  return true;
}

void startRuntimeMode() {
  Serial.println("Starting runtime mode");

  // Stop config server first
  if (configServerStarted) {
    configServer.stop();
    delay(100);  // Allow server to stop
    configServerStarted = false;
  }

  // Stop AP mode
  WiFi.softAPdisconnect(true);
  delay(100);

  // Initialize network interfaces
  bool networkStarted = false;

  if (config.ethernetEnabled) {
    startEthernet();
    networkStarted = eth_connected;
  }

  if (config.wifiEnabled && (!config.ethernetEnabled || !networkStarted)) {
    startWiFi();
    networkStarted = (WiFi.status() == WL_CONNECTED);
  }

  if (!networkStarted) {
    Serial.println("No network connection - fallback to AP mode");
    startConfigMode();
    return;
  }

  // Create web server task
  createWebServerTask();

  // Start runtime server
  if (!setupRuntimeWebServer()) {
    Serial.println("Failed to setup runtime server");
    startConfigMode();
    return;
  }

  runtimeServer.begin();
  runtimeServerStarted = true;
  Serial.println("Runtime server started");
}


void startEthernet() {
  Serial.println("\nInitializing Ethernet...");
  eth_connected = false;

  // Full hardware reset
  ETH.config(INADDR_NONE, INADDR_NONE, INADDR_NONE);
  ETH.begin(ETH_PHY_TYPE, ETH_PHY_ADDR, ETH_PHY_CS, ETH_PHY_IRQ, ETH_PHY_RST,
            ETH_PHY_SPI_HOST, ETH_PHY_SPI_SCK, ETH_PHY_SPI_MISO, ETH_PHY_SPI_MOSI);

  if (!config.useDHCP) {
    Serial.println("Using Static IP");
    ETH.config(config.staticIP, config.gateway, config.subnet);
  } else {
    Serial.println("Using DHCP");
  }

  // Wait for connection with timeout
  unsigned long startTime = millis();
  while (millis() - startTime < 15000) {
    if (ETH.linkUp()) {
      eth_connected = true;
      IPAddress ip = ETH.localIP();
      if (ip != INADDR_NONE) {
        Serial.printf("\nEthernet Connected!\nIP: %s\n", ip.toString().c_str());
        return;
      }
    }
    Serial.print(".");
    delay(500);
  }

  Serial.println("\nEthernet Failed to Get IP Address");
  ETH.end();
}

void startWiFi() {
  WiFi.mode(WIFI_STA);
  WiFi.begin(config.wifiSSID, config.wifiPassword);

  Serial.print("Connecting to WiFi ");
  Serial.print(config.wifiSSID);

  unsigned long startTime = millis();
  while (WiFi.status() != WL_CONNECTED && millis() - startTime < 10000) {
    delay(500);
    Serial.print(".");
  }

  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("\nConnected! IP address: ");
    Serial.println(WiFi.localIP());
  } else {
    Serial.println("\nFailed to connect to WiFi");
  }
}

// ==================== WEB SERVER FUNCTIONS ====================

void setupConfigWebServer() {
  // Configuration Page Handler
  configServer.on("/", HTTP_GET, []() {
    String html = R"=====(
<!DOCTYPE html><html><head><title>ESP32 Configuration</title>
<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}
.interface-badge {
    background: #4CAF50;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    display: inline-block;
    margin-bottom: 20px;
}
.section {
    border: 1px solid #ddd;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 5px;
}
label {
    display: block;
    margin: 10px 0;
}
input[type="text"], 
input[type="password"] {
    padding: 8px;
    width: 100%;
    box-sizing: border-box;
    margin-top: 5px;
}
button {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
}
</style>
</head>
<body>
<div class="interface-badge">Configuration Mode</div>
<h1>Network Configuration</h1>
<form action='/save' method='post'>
<div class='section'>
    <h2>WiFi Settings</h2>
    <label><input type='checkbox' name='wifiEnabled' %WIFI_ENABLED%> Enable WiFi</label>
    <label>SSID: <input type='text' name='wifiSSID' value='%WIFI_SSID%'></label>
    <label>Password: <input type='password' name='wifiPassword' value='%WIFI_PASS%'></label>
</div>
<div class='section'>
    <h2>Ethernet Settings</h2>
    <label><input type='checkbox' name='ethernetEnabled' %ETH_ENABLED%> Enable Ethernet</label>
    <label><input type='radio' name='ipMode' value='dhcp' %DHCP_CHECKED%> DHCP</label>
    <label><input type='radio' name='ipMode' value='static' %STATIC_CHECKED%> Static IP</label>
    <label>IP Address: <input type='text' name='staticIP' value='%STATIC_IP%'></label>
    <label>Subnet Mask: <input type='text' name='subnet' value='%SUBNET%'></label>
    <label>Gateway: <input type='text' name='gateway' value='%GATEWAY%'></label>
</div>
<button type='submit'>Save Configuration</button>
</form>
</body>
</html>
)=====";

    // Replace placeholders
    html.replace("%WIFI_ENABLED%", config.wifiEnabled ? "checked" : "");
    html.replace("%WIFI_SSID%", String(config.wifiSSID));
    html.replace("%WIFI_PASS%", String(config.wifiPassword));
    html.replace("%ETH_ENABLED%", config.ethernetEnabled ? "checked" : "");
    html.replace("%DHCP_CHECKED%", config.useDHCP ? "checked" : "");
    html.replace("%STATIC_CHECKED%", !config.useDHCP ? "checked" : "");
    html.replace("%STATIC_IP%", config.staticIP.toString());
    html.replace("%SUBNET%", config.subnet.toString());
    html.replace("%GATEWAY%", config.gateway.toString());

    configServer.send(200, "text/html", html);
  });

  // Save Configuration Handler
  configServer.on("/save", HTTP_POST, []() {
    // Validate IPs first
    if (!config.staticIP.fromString(configServer.arg("staticIP")) || !config.subnet.fromString(configServer.arg("subnet")) || !config.gateway.fromString(configServer.arg("gateway"))) {
      configServer.send(400, "text/plain", "Invalid IP configuration");
      return;
    }

    // Update configuration
    config.wifiEnabled = configServer.hasArg("wifiEnabled");
    strncpy(config.wifiSSID, configServer.arg("wifiSSID").c_str(), sizeof(config.wifiSSID));
    strncpy(config.wifiPassword, configServer.arg("wifiPassword").c_str(), sizeof(config.wifiPassword));
    config.ethernetEnabled = configServer.hasArg("ethernetEnabled");
    config.useDHCP = configServer.arg("ipMode") == "dhcp";

    // Save and restart
    saveConfiguration();

    String html = R"=====(
<!DOCTYPE html><html><head>
<title>Configuration Saved</title>
<meta http-equiv='refresh' content='5;url=/'>
<style>
body {
    font-family: Arial, sans-serif;
    text-align: center;
    padding: 50px;
}
</style>
</head>
<body>
<h1>Configuration Saved</h1>
<p>The device will restart in runtime mode shortly.</p>
</body>
</html>
)=====";

    configServer.send(200, "text/html", html);
    delay(2000);
    restartNetworkInterfaces();
  });

  configServer.onNotFound([]() {
    configServer.send(404, "text/plain", "Not found");
  });

  configServer.begin();
  Serial.println("Configuration server started on ***********");
}

// Runtime Server (STA WiFi + Ethernet - Port 80)
bool setupRuntimeWebServer() {
  // Clear any previous handlers
  runtimeServer.close();

  // Unprotected routes
  runtimeServer.on("/", HTTP_GET, []() {
    handleRoot(runtimeServer);
  });

  runtimeServer.on("/login", HTTP_POST, []() {
    handleLogin(runtimeServer);
  });

  runtimeServer.on("/logout", HTTP_GET, []() {
    handleLogout(runtimeServer);
  });

  // Protected routes
  runtimeServer.on("/control", HTTP_GET, []() {
    protectedRoute(runtimeServer, handleControl);
  });

  runtimeServer.on("/weekday", HTTP_GET, []() {
    protectedRoute(runtimeServer, handleWeekday);
  });

  runtimeServer.on("/holiday", HTTP_GET, []() {
    protectedRoute(runtimeServer, handleHoliday);
  });

  runtimeServer.on("/holiday/enter", HTTP_GET, []() {
    protectedRoute(runtimeServer, handleHolidayEnter);
  });

  runtimeServer.on("/holiday/schedule", HTTP_GET, []() {
    protectedRoute(runtimeServer, handleHolidaySchedule);
  });

  runtimeServer.on("/holiday/delete", HTTP_GET, []() {
    protectedRoute(runtimeServer, handleHolidayDelete);
  });

  runtimeServer.on("/setdatetime", HTTP_GET, []() {
    protectedRoute(runtimeServer, handleSetDateTime);
  });

  runtimeServer.on("/setdatetime", HTTP_POST, []() {
    protectedRoute(runtimeServer, handleSetDateTime);
  });

  runtimeServer.on("/setntp", HTTP_POST, []() {
    protectedRoute(runtimeServer, handleSetNTP);
  });

  runtimeServer.on("/updatentpnow", HTTP_POST, []() {
    protectedRoute(runtimeServer, handleUpdateNTPNow);
  });

  runtimeServer.on("/wifisettings", HTTP_GET, []() {
    protectedRoute(runtimeServer, handleWiFiSettings);
  });

  runtimeServer.on("/savewifi", HTTP_POST, []() {
    protectedRoute(runtimeServer, handleSaveWiFi);
  });

  runtimeServer.on("/update", HTTP_GET, []() {
    protectedRoute(runtimeServer, handleUpdateGet);
  });

  runtimeServer.on("/update", HTTP_POST, []() {
    protectedRoute(runtimeServer, handleUpdatePost);
  });

  runtimeServer.on("/update", HTTP_PUT, []() {
    protectedRoute(runtimeServer, handleUpdateUpload);
  });

  runtimeServer.on("/save/weekday", HTTP_POST, []() {
    protectedRoute(runtimeServer, handleSaveWeekday);
  });

  runtimeServer.on("/save/holiday", HTTP_POST, []() {
    protectedRoute(runtimeServer, handleSaveHoliday);
  });

  runtimeServer.on("/add/holiday", HTTP_POST, []() {
    protectedRoute(runtimeServer, handleAddHoliday);
  });

  runtimeServer.on("/delete/holidays", HTTP_POST, []() {
    protectedRoute(runtimeServer, handleDeleteHolidays);
  });

  // 404 Handler
  runtimeServer.onNotFound([]() {
    if (!processRequest(runtimeServer, false)) return;

    if (checkAuthentication(runtimeServer)) {
      runtimeServer.send(404, "text/html", "<h1>404 - Authenticated</h1>");
    } else {
      runtimeServer.send(404, "text/html", "<h1>404 - Not Found</h1>");
    }
  });
  // Start server on available interfaces
  bool success = false;

  if (config.ethernetEnabled && eth_connected) {
    runtimeServer.begin();
    Serial.print("Ethernet server running on ");
    Serial.println(ETH.localIP());
    success = true;
  }

  if (config.wifiEnabled && WiFi.status() == WL_CONNECTED) {
    runtimeServer.begin();
    Serial.print("WiFi STA server running on ");
    Serial.println(WiFi.localIP());
    success = true;
  }

  if (!success) {
    Serial.println("Failed to start runtime server - no active network interfaces");
  }

  return success;
}

void webServerTask(void* pvParameters) {
  Serial.println("WebServer task started");

  while (true) {
    if (configServerStarted) {
      configServer.handleClient();
    }

    if (runtimeServerStarted) {
      runtimeServer.handleClient();
    }

    // Check for termination request
    if (ulTaskNotifyTake(pdTRUE, pdMS_TO_TICKS(10))) {
      break;
    }
  }

  // Cleanup before exiting
  if (runtimeServerStarted) {
    runtimeServer.close();
  }
  if (configServerStarted) {
    configServer.close();
  }

  vTaskDelete(NULL);
}

void configModeTimeoutCallback(TimerHandle_t xTimer) {
  // Don't perform complex operations in timer callbacks
  // Just set a flag and handle in main loop
  configModeTimeout = true;
}

void checkServerStatus() {
  static unsigned long lastCheck = 0;
  if (millis() - lastCheck > 10000) {  // Check every 10 seconds
    lastCheck = millis();

    if (runtimeServerStarted) {
      if (!testServerConnection()) {
        Serial.println("Runtime server not responding, restarting...");
        restartNetworkInterfaces();
      }
    }
  }
}

void networkMonitorTask(void* pvParameters) {
  //esp_task_wdt_add(NULL);
  Serial.println("Network Monitor task started.");

  while (true) {
    checkNetworkStatus();
    // if (netMonitorTaskHandle != NULL) {
    //     esp_task_wdt_reset();  // Explicit feed
    // }
    vTaskDelay(pdMS_TO_TICKS(2000));  // Check every 2 seconds
  }
}


void checkNetworkStatus() {
  static uint8_t ethFailCount = 0;
  static uint8_t wifiFailCount = 0;

  // Ethernet check
  bool currentEth = (config.ethernetEnabled && ETH.linkUp());
  if (currentEth != eth_connected) {
    eth_connected = currentEth;
    Serial.printf("Ethernet %s\n", eth_connected ? "connected" : "disconnected");

    if (!eth_connected && ethFailCount++ > 3) {
      Serial.println("Ethernet unstable, reinitializing");
      startEthernet();
      ethFailCount = 0;
    }
  }

  // WiFi check
  bool currentWifi = (config.wifiEnabled && WiFi.status() == WL_CONNECTED);
  if (currentWifi != wifi_sta_connected) {
    wifi_sta_connected = currentWifi;
    Serial.printf("WiFi %s\n", wifi_sta_connected ? "connected" : "disconnected");

    if (!wifi_sta_connected && wifiFailCount++ > 3) {
      Serial.println("WiFi unstable, reconnecting");
      startWiFi();
      wifiFailCount = 0;
    }
  }

  // Emergency fallback if no connections
  if (config.ethernetEnabled && !eth_connected && config.wifiEnabled && !wifi_sta_connected) {
    Serial.println("No active connections, fallback to AP");
    WiFi.softAP(ap_ssid, ap_password);
  }
}

// ==================== WATCHDOG FUNCTIONS ====================

// bool initWatchdog() {
//     esp_task_wdt_deinit(); // Deinitialize first

//     esp_task_wdt_config_t twdt_config = {
//         .timeout_ms = 30000,          // Increased to 30 seconds
//         .idle_core_mask = (1 << 0) | (1 << 1), // Watch both cores
//         .trigger_panic = true
//     };

//     esp_err_t err = esp_task_wdt_init(&twdt_config);
//     if (err != ESP_OK) {
//         Serial.printf("Watchdog init failed: %s\n", esp_err_to_name(err));
//         return false;
//     }
//     return true;
// }


// void feedWatchdog() {
//   static unsigned long lastFeed = 0;
//   unsigned long now = millis();

//   if (now - lastFeed > 500) {
//     lastFeed = now;
//     esp_task_wdt_reset();
//   }
// }

// void criticalOperation() {
//   esp_task_wdt_delete(NULL);
//   // Perform time-sensitive operation
//   esp_task_wdt_add(NULL);
// }

// ==================== UTILITY FUNCTIONS ====================

void printNetworkSettings() {
  Serial.println("\nCurrent Network Configuration:");
  Serial.printf("Ethernet Enabled: %s\n", config.ethernetEnabled ? "Yes" : "No");
  Serial.printf("Use DHCP: %s\n", config.useDHCP ? "Yes" : "No");
  if (!config.useDHCP) {
    Serial.printf("Static IP: %s\n", config.staticIP.toString().c_str());
    Serial.printf("Subnet: %s\n", config.subnet.toString().c_str());
    Serial.printf("Gateway: %s\n", config.gateway.toString().c_str());
  }
  if (eth_connected) {
    Serial.printf("MAC Address: %s\n", ETH.macAddress().c_str());
  } else {
    Serial.println("MAC Address: Not connected");
  }
}

void printSystemInfo() {
  Serial.println("\nSystem Status:");
  Serial.printf("Free Heap: %d bytes\n", ESP.getFreeHeap());

  if (webServerTaskHandle != NULL) {
    Serial.println("WebServer task: Running");
  }

  if (netMonitorTaskHandle != NULL) {
    Serial.println("NetMonitor task: Running");
  }

  if (config.ethernetEnabled) {
    Serial.printf("Ethernet: %s\n", eth_connected ? "Connected" : "Disconnected");
  }
}

void restartWiFi() {
  Serial.println("Restarting WiFi connection...");
  WiFi.disconnect();
  delay(500);
  if (config.wifiEnabled) {
    startWiFi();
  }
}

void restartEthernet() {
  Serial.println("Restarting Ethernet connection...");
  ETH.config(INADDR_NONE, INADDR_NONE, INADDR_NONE);
  delay(100);
  if (config.ethernetEnabled) {
    startEthernet();
  }
}

void restartNetworkInterfaces() {
  Serial.println("Restarting network interfaces...");

  // Stop tasks safely
  safeDeleteTask(&webServerTaskHandle);
  safeDeleteTask(&netMonitorTaskHandle);

  // Stop servers
  configServer.stop();
  runtimeServer.stop();
  delay(100);

  // Full Ethernet restart
  ETH.end();
  delay(1000);  // Important delay for hardware reset

  // Reinitialize
  startEthernet();

  // Restart servers if we have a connection
  if (eth_connected || wifi_sta_connected) {
    runtimeServer.begin();
    Serial.println("Servers restarted");
  }

  // Recreate tasks
  safeCreateTask(&webServerTaskHandle, webServerTask, "WebServer", 12288, 2, 1);
  safeCreateTask(&netMonitorTaskHandle, networkMonitorTask, "NetMonitor", 4096, 1, 0);
}

bool validateNetworkConfig() {
  if (!config.useDHCP) {
    if (config.staticIP == INADDR_NONE || config.gateway == INADDR_NONE || config.subnet == INADDR_NONE) {
      Serial.println("Invalid Network Configuration!");
      return false;
    }

    if (config.staticIP[0] == 0 || config.gateway[0] == 0 || config.subnet[0] == 0) {
      Serial.println("Network configuration contains zeros!");
      return false;
    }
  }
  return true;
}

bool testServerConnection() {
  if (!eth_connected) return false;

  HTTPClient http;
  String url = "http://" + ETH.localIP().toString() + "/";
  http.begin(url);

  int httpCode = http.GET();
  bool success = (httpCode == HTTP_CODE_OK);
  http.end();

  return success;
}

// ==================== BELLBOT PROJECT FUNCTIONS ====================

void saveWiFiSettings() {
  DynamicJsonDocument doc(256);
  doc["sta_mode"] = wifi_sta_mode;
  doc["sta_ssid"] = sta_ssid;
  doc["sta_password"] = sta_password;

  File file = SPIFFS.open("/wifi.json", "w");
  if (!file) {
    Serial.println("Failed to open wifi config file for writing");
    return;
  }

  serializeJson(doc, file);
  file.close();
}

void loadWiFiSettings() {
  if (!SPIFFS.exists("/wifi.json")) {
    return;
  }

  File file = SPIFFS.open("/wifi.json", "r");
  if (!file) {
    Serial.println("Failed to open wifi config file");
    return;
  }

  DynamicJsonDocument doc(256);
  DeserializationError error = deserializeJson(doc, file);
  if (error) {
    Serial.println("Failed to parse wifi config file");
    return;
  }

  wifi_sta_mode = doc["sta_mode"];
  strncpy((char*)sta_ssid, doc["sta_ssid"], 32);
  strncpy((char*)sta_password, doc["sta_password"], 64);

  file.close();
}

// void updateDisplay() {
//   // Only update at fixed intervals
//   if (millis() - lastDisplayUpdate < DISPLAY_UPDATE_INTERVAL) {
//     return;
//   }
//   lastDisplayUpdate = millis();

//   // Clear display first
//   tft.fillScreen(TFT_BLACK);

//   try {
//     DateTime now;
//     if (rtcInitialized) {
//       now = rtc.now();  // This was crashing in your stack trace
//     } else {
//       // Fallback to software time if RTC fails
//       now = DateTime(millis() / 1000);
//     }

//     // Rest of your display code...
//     const char* days[] = { "Sun", "Mon", "Tue", "Wed", "Thurs", "Fri", "Sat" };
//     String currentDay = days[now.dayOfTheWeek()];
//     String currentDate = formatDate(now.year(), now.month(), now.day());
//     String currentTime = formatTime(now.hour(), now.minute()) + ":" + (now.second() < 10 ? "0" : "") + String(now.second());

//     // Title Section
//     tft.fillRect(0, 0, 356, 25, TFT_PURPLE);
//     tft.setTextColor(TFT_WHITE, TFT_PURPLE);
//     tft.setTextDatum(MC_DATUM);
//     tft.drawString("Bell On Time v1.1", 120, 15, 4);

//     // ... [rest of your display code]

//   } catch (...) {
//     Serial.println("Error during display update");
//     // Basic error display
//     tft.setTextColor(TFT_RED, TFT_BLACK);
//     tft.drawString("Display Error", 10, 10, 2);
//   }
// }

String calculateNextAlarm() {
  DateTime now = rtc.now();
  String currentTime = formatTime(now.hour(), now.minute());
  String currentDate = formatDate(now.year(), now.month(), now.day());
  int currentDayOfWeek = now.dayOfTheWeek();

  // 1. First check if today is a holiday and has remaining alarms
  if (holidays.containsKey(currentDate)) {
    JsonObject holiday = holidays[currentDate];

    for (int i = 0; i < 5; i++) {
      JsonObject zone = holiday["zones"][i];
      if (zone["enabled"] && zone["time"].as<String>() > currentTime) {
        return zone["time"].as<String>();
      }
    }
  }
  // 2. Check today's weekday schedule if not holiday
  else {
    DaySchedule day = weekSchedule[currentDayOfWeek];
    for (int i = 0; i < 10; i++) {
      if (day.zones[i].enabled && day.zones[i].startTime > currentTime) {
        return day.zones[i].startTime;
      }
    }
  }

  // 3. If no more alarms today, check tomorrow
  DateTime tomorrow = now + TimeSpan(1, 0, 0, 0);
  String tomorrowDate = formatDate(tomorrow.year(), tomorrow.month(), tomorrow.day());
  int tomorrowDayOfWeek = tomorrow.dayOfTheWeek();

  // Check if tomorrow is a holiday
  if (holidays.containsKey(tomorrowDate)) {
    JsonObject holiday = holidays[tomorrowDate];
    for (int i = 0; i < 5; i++) {
      if (holiday["zones"][i]["enabled"]) {
        return holiday["zones"][i]["time"].as<String>();
      }
    }
  }
  // Check tomorrow's weekday schedule
  else {
    DaySchedule day = weekSchedule[tomorrowDayOfWeek];
    for (int i = 0; i < 10; i++) {
      if (day.zones[i].enabled) {
        return day.zones[i].startTime;
      }
    }
  }

  return "None";
}

void checkAlarms() {
  DateTime now = rtc.now();
  String currentTime = formatTime(now.hour(), now.minute());
  String currentDate = formatDate(now.year(), now.month(), now.day());
  int currentDayOfWeek = now.dayOfTheWeek();

  if (alarmActive) {
    if (millis() - alarmStartTime >= alarmDuration) {
      deactivateAlarm();
    }
    return;
  }

  if (holidays.containsKey(currentDate)) {
    JsonObject holiday = holidays[currentDate];
    alarmDuration = holiday["defaultDuration"];

    for (int i = 0; i < 5; i++) {
      JsonObject zone = holiday["zones"][i];
      if (zone["enabled"] && zone["time"] == currentTime && currentTime != lastTriggeredAlarmTime) {
        activateAlarm();
        lastTriggeredAlarmTime = currentTime;
        return;
      }
    }
  } else {
    DaySchedule day = weekSchedule[currentDayOfWeek];
    alarmDuration = day.defaultDuration;

    for (int i = 0; i < 10; i++) {
      if (day.zones[i].enabled && day.zones[i].startTime == currentTime && currentTime != lastTriggeredAlarmTime) {
        activateAlarm();
        lastTriggeredAlarmTime = currentTime;
        return;
      }
    }
  }
}

void activateAlarm() {
  alarmDuration = min(alarmDuration, (uint32_t)60000);

  pcf.write(2, LOW);
  pcf.write(3, LOW);
  alarmActive = true;
  displayBlinkState = true;
  lastBlinkTime = millis();
  alarmStartTime = millis();
  lastTriggeredAlarmTime = formatTime(rtc.now().hour(), rtc.now().minute());
  //updateDisplay();
  Serial.println("Alarm activated for " + String(alarmDuration) + "ms");
}

void deactivateAlarm() {
  pcf.write(2, HIGH);
  pcf.write(3, HIGH);
  alarmActive = false;
  displayBlinkState = true;
  //updateDisplay();
  Serial.println("Alarm deactivated");
}

String formatTime(int hour, int minute) {
  String h = hour < 10 ? "0" + String(hour) : String(hour);
  String m = minute < 10 ? "0" + String(minute) : String(minute);
  return h + ":" + m;
}

String formatDate(int year, int month, int day) {
  String y = String(year);
  String m = month < 10 ? "0" + String(month) : String(month);
  String d = day < 10 ? "0" + String(day) : String(day);
  return y + "-" + m + "-" + d;
}

void loadSettings() {
  if (!SPIFFS.exists(CONFIG_FILE)) {
    initializeDefaultSettings();
    return;
  }

  File file = SPIFFS.open(CONFIG_FILE, "r");
  if (!file) {
    Serial.println("Failed to open config file");
    initializeDefaultSettings();
    return;
  }

  DynamicJsonDocument doc(4096);
  DeserializationError error = deserializeJson(doc, file);
  if (error) {
    Serial.println("Failed to parse config file");
    initializeDefaultSettings();
    return;
  }

  JsonArray weekArray = doc["weekSchedule"];
  for (int i = 0; i < 7 && i < weekArray.size(); i++) {
    JsonObject day = weekArray[i];
    weekSchedule[i].defaultDuration = day["defaultDuration"];

    JsonArray zones = day["zones"];
    for (int j = 0; j < 10 && j < zones.size(); j++) {
      JsonObject zone = zones[j];
      weekSchedule[i].zones[j].startTime = zone["time"].as<String>();
      weekSchedule[i].zones[j].enabled = zone["enabled"];
    }
  }

  JsonObject holidaysObj = doc["holidays"].as<JsonObject>();
  holidays.clear();
  for (JsonPair kv : holidaysObj) {
    holidays[kv.key().c_str()] = kv.value();
  }

  file.close();
}

void updateTimeFromNTP() {
  if (WiFi.status() != WL_CONNECTED) return;

  timeClient.update();
  time_t epochTime = timeClient.getEpochTime();
  const long timezoneOffset = 5.5 * 3600;
  epochTime += timezoneOffset;

  struct tm* timeinfo;
  timeinfo = localtime(&epochTime);

  rtc.adjust(DateTime(
    timeinfo->tm_year + 1900,
    timeinfo->tm_mon + 1,
    timeinfo->tm_mday,
    timeinfo->tm_hour,
    timeinfo->tm_min,
    timeinfo->tm_sec));

  Serial.printf("RTC Updated: %02d:%02d:%02d\n",
                timeinfo->tm_hour, timeinfo->tm_min, timeinfo->tm_sec);
}

void saveSettings() {
  DynamicJsonDocument doc(4096);

  JsonArray weekArray = doc.createNestedArray("weekSchedule");
  for (int i = 0; i < 7; i++) {
    JsonObject day = weekArray.createNestedObject();
    day["defaultDuration"] = weekSchedule[i].defaultDuration;

    JsonArray zones = day.createNestedArray("zones");
    for (int j = 0; j < 10; j++) {
      JsonObject zone = zones.createNestedObject();
      zone["time"] = weekSchedule[i].zones[j].startTime;
      zone["enabled"] = weekSchedule[i].zones[j].enabled;
    }
  }

  doc["holidays"] = holidays;

  File file = SPIFFS.open(CONFIG_FILE, "w");
  if (!file) {
    Serial.println("Failed to open config file for writing");
    return;
  }

  serializeJson(doc, file);
  file.close();
}

void initializeDefaultSettings() {
  const char* days[] = { "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday" };

  for (int i = 0; i < 7; i++) {
    weekSchedule[i].dayName = days[i];
    weekSchedule[i].defaultDuration = 1000;

    for (int j = 0; j < 10; j++) {
      weekSchedule[i].zones[j].id = j;
      weekSchedule[i].zones[j].startTime = "08:00";
      weekSchedule[i].zones[j].enabled = (i >= 1 && i <= 5 && j == 0);
    }
  }

  saveSettings();
}

WebServer& getActiveServer() {
  // We now only use runtimeServer for all requests after configuration
  return runtimeServer;
}
// ==================== WEB PAGE HANDLERS ====================

void handleRoot(WebServer& server) {
  if (checkAuthentication(server)) {
    server.sendHeader("Location", "/control");
    server.send(301);
  } else {
    String html = getHomePage();
    server.send(200, "text/html", html);
  }
}

void handleLogin(WebServer& server) {
  Serial.println("Login handler called");
  if (server.method() == HTTP_POST) {
    Serial.println("POST request received");
    if (server.hasArg("username") && server.hasArg("password")) {
      Serial.println("Username and password received");
      if (server.arg("username") == auth_username && server.arg("password") == auth_password) {
        Serial.println("Credentials matched");

        // Generate session ID and immediately store it
        String newSessionId = String(esp_random(), HEX) + String(micros(), HEX);
        currentSessionId = newSessionId;  // Store in global variable
        lastActivityTime = millis();

        Serial.println("Generated session ID: " + currentSessionId);

        String cookie = "ESPSESSIONID=" + currentSessionId + "; Path=/; HttpOnly; SameSite=Strict; Max-Age=300";

        Serial.println("Setting cookie: " + cookie);
        Serial.println("Redirecting to /control");

        server.sendHeader("Location", "/control");
        server.sendHeader("Set-Cookie", cookie);
        server.send(303);
        return;
      }
    }
    Serial.println("Login failed - redirecting to /");
    server.sendHeader("Location", "/?error=1");
    server.send(303);
  } else {
    server.send(405, "text/plain", "Method Not Allowed");
  }
}

void handleLogout(WebServer& server) {
  currentSessionId = "";
  server.sendHeader("Location", "/");
  server.sendHeader("Set-Cookie", "ESPSESSIONID=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT");
  server.send(301);
}

void handleControl(WebServer& server) {
  if (!checkAuthentication(server)) {
    server.sendHeader("Location", "/");
    server.send(303);
    return;
  }
  String html = R"=====(
<!DOCTYPE html>
<html>
<head>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Control Panel</title>
  )=====";

  html += getGlobalStyles();

  html += R"=====(
  <style>
    .logout-btn {
      position: absolute;
      top: 20px;
      right: 20px;
      padding: 8px 15px;
      background-color: #dc3545;
      color: white;
      text-decoration: none;
      border-radius: 5px;
      font-size: 14px;
    }
    .container {
      margin-bottom: 30px;
    }
  </style>
</head>
<body>
  <a href="/logout" class="logout-btn">Logout</a>
  
  <div class="container">
    <h1>Control Panel</h1>
    
    <div class="container">
      <h2>Scheduling</h2>
      <a href="/weekday" class="btn">Weekday Schedule</a>
      <a href="/holiday" class="btn">Holiday Schedule</a>
    </div>
    
    <div class="container">
      <h2>System Settings</h2>
      <a href="/setdatetime" class="btn">RTC Settings</a>
      <a href="/update" class="btn">Firmware Update</a>
      <a href="/wifisettings" class="btn">WiFi Settings</a>
    </div>
    
    <div class="container">
      <h2>System Info</h2>
      <p>Current Time: )=====";

  DateTime now = rtc.now();
  html += String(now.year()) + "-";
  html += (now.month() < 10 ? "0" : "") + String(now.month()) + "-";
  html += (now.day() < 10 ? "0" : "") + String(now.day()) + " ";
  html += (now.hour() < 10 ? "0" : "") + String(now.hour()) + ":";
  html += (now.minute() < 10 ? "0" : "") + String(now.minute());

  html += R"=====(</p>
    </div>
    
    <div id="session-timer"></div>
  </div>
  )=====";

  html += getGlobalScripts();
  html += R"=====(
</body>
</html>
)=====";

  server.send(200, "text/html", html);
}

void handleWeekday(WebServer& server) {
  currentView = "weekday";
  int dayIndex = server.hasArg("day") ? server.arg("day").toInt() : 1;
  if (dayIndex < 0 || dayIndex > 6) dayIndex = 1;
  server.send(200, "text/html", getWeekdayPage(server));
}


void handleHoliday(WebServer& server) {
  currentView = "holiday";
  server.send(200, "text/html", getHolidayPage());
}

void handleHolidayEnter(WebServer& server) {
  currentView = "holiday_enter";
  server.send(200, "text/html", getHolidayEnterPage());
}

void handleHolidaySchedule(WebServer& server) {
  currentView = "holiday_schedule";
  selectedHoliday = server.arg("date");
  server.send(200, "text/html", getHolidaySchedulePage());
}

void handleHolidayDelete(WebServer& server) {
  currentView = "holiday_delete";
  server.send(200, "text/html", getHolidayDeletePage());
}

void handleSetDateTime(WebServer& server) {
  if (server.method() == HTTP_POST) {
    if (server.hasArg("date") && server.hasArg("time")) {
      String dateStr = server.arg("date");
      String timeStr = server.arg("time");

      int year = dateStr.substring(0, 4).toInt();
      int month = dateStr.substring(5, 7).toInt();
      int day = dateStr.substring(8, 10).toInt();
      int hour = timeStr.substring(0, 2).toInt();
      int minute = timeStr.substring(3, 5).toInt();

      rtc.adjust(DateTime(year, month, day, hour, minute, 0));
      server.send(200, "text/html",
                  "<script>alert('Date/time set!');window.location='/control';</script>");
      return;
    }
    server.send(400, "text/plain", "Invalid parameters");
  } else {
    server.send(200, "text/html", getRTCPage());
  }
}

void handleSaveWeekday(WebServer& server) {
  String day = server.arg("day");
  bool applyToAll = server.hasArg("applyToAll");
  uint32_t durationMs = static_cast<uint32_t>(server.arg("duration").toFloat() * 1000);

  TimeZone zones[10];
  for (int i = 0; i < 10; i++) {
    zones[i].startTime = server.arg("time" + String(i));
    zones[i].enabled = server.hasArg("enable" + String(i));
  }

  if (applyToAll) {
    for (int d = 0; d < 7; d++) {
      weekSchedule[d].defaultDuration = durationMs;
      for (int i = 0; i < 10; i++) {
        weekSchedule[d].zones[i] = zones[i];
      }
    }
  } else {
    int dayIndex = day.toInt();
    weekSchedule[dayIndex].defaultDuration = durationMs;
    for (int i = 0; i < 10; i++) {
      weekSchedule[dayIndex].zones[i] = zones[i];
    }
  }

  saveSettings();
  server.send(200, "text/html",
              "<script>alert('Saved!');window.location='/weekday?day=" + day + "';</script>");
}

void handleSaveHoliday(WebServer& server) {
  String date = server.arg("date");
  uint32_t durationMs = static_cast<uint32_t>(server.arg("duration").toFloat() * 1000);

  if (!holidays.containsKey(date)) {
    server.send(400, "text/plain", "Holiday not found");
    return;
  }

  JsonObject holiday = holidays[date];
  holiday["defaultDuration"] = durationMs;

  for (int i = 0; i < 5; i++) {
    holiday["zones"][i]["time"] = server.arg("time" + String(i));
    holiday["zones"][i]["enabled"] = server.hasArg("enable" + String(i));
  }

  saveSettings();
  server.send(200, "text/html",
              "<script>alert('Holiday saved!');window.location='/holiday/schedule?date=" + date + "';</script>");
}

void handleAddHoliday(WebServer& server) {
  String date = server.arg("date");

  if (date == "") {
    server.send(400, "text/plain", "Date required");
    return;
  }

  if (!holidays.containsKey(date)) {
    JsonObject holiday = holidays.createNestedObject(date);
    holiday["defaultDuration"] = 1000;

    JsonArray zones = holiday.createNestedArray("zones");
    for (int i = 0; i < 5; i++) {
      JsonObject zone = zones.createNestedObject();
      zone["time"] = "08:00";
      zone["enabled"] = false;
    }

    saveSettings();
    server.send(200, "text/html",
                "<script>alert('Holiday added!');window.location='/holiday/schedule?date=" + date + "';</script>");
  } else {
    server.send(200, "text/html",
                "<script>alert('Holiday exists!');window.location='/holiday';</script>");
  }
}

void handleDeleteHolidays(WebServer& server) {
  for (int i = 0; i < server.args(); i++) {
    if (server.argName(i).startsWith("holiday_")) {
      String date = server.argName(i).substring(8);
      holidays.remove(date);
    }
  }

  saveSettings();
  server.send(200, "text/html",
              "<script>alert('Holidays deleted!');window.location='/holiday';</script>");
}

void handleSetNTP(WebServer& server) {
  if (server.hasArg("ntpServer") && server.hasArg("ntpEnabled")) {
    ntpServer = server.arg("ntpServer");
    ntpEnabled = server.arg("ntpEnabled") == "on";
    timeClient.setPoolServerName(ntpServer.c_str());
    server.send(200, "text/html",
                "<script>alert('NTP settings saved!');window.location='/setdatetime';</script>");
  } else {
    server.send(400, "text/plain", "Missing parameters");
  }
}

void handleUpdateNTPNow(WebServer& server) {
  updateTimeFromNTP();
  server.send(200, "text/html",
              "<script>alert('Time updated from NTP!');window.location='/setdatetime';</script>");
}

void handleWiFiSettings(WebServer& server) {
  server.send(200, "text/html", getWiFiSettingsPage());
}

void setupWiFi() {
  if (wifi_sta_mode && sta_ssid[0] != '\0') {
    WiFi.mode(WIFI_STA);
    WiFi.begin(sta_ssid, sta_password);
  } else {
    WiFi.mode(WIFI_AP);
    WiFi.softAPConfig(local_ip, gateway, subnet);
    WiFi.softAP(ap_ssid, ap_password);
  }
}

void handleSaveWiFi(WebServer& server) {
  wifi_sta_mode = server.hasArg("sta_mode");

  if (wifi_sta_mode) {
    strncpy(sta_ssid, server.arg("sta_ssid").c_str(), sizeof(sta_ssid));
    strncpy(sta_password, server.arg("sta_password").c_str(), sizeof(sta_password));
  }

  saveWiFiSettings();
  setupWiFi();

  server.send(200, "text/html",
              "<script>alert('WiFi settings saved!');window.location='/wifisettings';</script>");
}

void handleUpdateGet(WebServer& server) {
  if (!otaUpdating) {
    server.sendHeader("Connection", "close");
    server.send(200, "text/html", updateHTML());
  } else {
    unsigned long elapsed = (millis() - otaStartTime) / 1000;
    server.send(200, "text/plain", "OTA in progress (" + String(elapsed) + "s)");
  }
}

void handleUpdatePost(WebServer& server) {
  if (Update.hasError()) {
    server.send(500, "text/plain", "Update failed");
  } else {
    // Clean up before restart
    if (webServerTaskHandle != NULL) {
      //esp_task_wdt_delete(webServerTaskHandle);
      vTaskDelete(webServerTaskHandle);
      webServerTaskHandle = NULL;
    }

    if (netMonitorTaskHandle != NULL) {
      //esp_task_wdt_delete(netMonitorTaskHandle);
      vTaskDelete(netMonitorTaskHandle);
      netMonitorTaskHandle = NULL;
    }

    server.send(200, "text/html",
                "<script>alert('Update successful! Rebooting...');setTimeout(function(){window.location='/';},3000);</script>");
    delay(1000);
    ESP.restart();
  }
}

void handleUpdateUpload(WebServer& server) {
  HTTPUpload& upload = server.upload();
  if (upload.status == UPLOAD_FILE_START) {
    otaUpdating = true;
    otaStartTime = millis();
    Serial.printf("Update: %s\n", upload.filename.c_str());
    uint32_t maxSketchSpace = (ESP.getFreeSketchSpace() - 0x1000) & 0xFFFFF000;
    if (!Update.begin(maxSketchSpace)) {
      Update.printError(Serial);
    }
  } else if (upload.status == UPLOAD_FILE_WRITE) {
    if (Update.write(upload.buf, upload.currentSize) != upload.currentSize) {
      Update.printError(Serial);
    }
    Serial.printf("Progress: %d%%\n", (upload.totalSize * 100) / Update.size());
  } else if (upload.status == UPLOAD_FILE_END) {
    if (Update.end(true)) {
      Serial.printf("Update Success: %u\n", upload.totalSize);
    } else {
      Update.printError(Serial);
    }
    Serial.setDebugOutput(false);
  } else if (upload.status == UPLOAD_FILE_ABORTED) {
    otaUpdating = false;
    Update.end();
    Serial.println("Update aborted");
  }
}

void createWebServerTask() {
  // First safely delete any existing task
  safeDeleteTask(&webServerTaskHandle);

  // Create new task with sufficient stack
  if (xTaskCreatePinnedToCore(
        webServerTask,
        "WebServer",
        12288,  // 12KB stack
        NULL,
        2,  // Higher priority
        &webServerTaskHandle,
        1)
      != pdPASS) {
    Serial.println("Failed to create web server task");
    webServerTaskHandle = NULL;
  }
}

void safeCreateTask(TaskHandle_t* handle, TaskFunction_t taskFunction,
                    const char* name, uint16_t stackSize, UBaseType_t priority,
                    BaseType_t coreID) {
  if (*handle == NULL) {
    if (xTaskCreatePinnedToCore(
          taskFunction,
          name,
          stackSize,
          NULL,
          priority,
          handle,
          coreID)
        == pdPASS) {

      // Only add to watchdog if not already added
      // if (esp_task_wdt_add(*handle) != ESP_OK) {
      //   Serial.printf("Task %s already in watchdog\n", name);
      // }
    }
  }
}

void safeDeleteTask(TaskHandle_t* handle) {
  if (*handle != NULL) {
    vTaskDelete(*handle);
    *handle = NULL;
    delay(50);  // Allow time for cleanup
  }
}


// ==================== WEB PAGE GENERATORS ====================

String getHomePage() {
  String html = R"=====(
<!DOCTYPE html>
<html>
<head>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bell On Time</title>
  )=====";

  html += getGlobalStyles();

  html += R"=====(
  <style>
    .login-container {
      background: white;
      padding: 30px;
      border-radius: 20px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.15);
      max-width: 500px;
      width: 90%;
      margin: 20px auto;
    }
    .form-group {
      margin-bottom: 20px;
      text-align: center;
    }
    .login-btn {
      width: 100%;
      padding: 12px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 5px;
      font-size: 16px;
      cursor: pointer;
    }
    .error {
      color: red;
      margin-bottom: 15px;
    }
  </style>
</head>
<body>
  <div class="login-container">
    <h1>Bell On Time</h1>
    <p>Print Electronics Equipments Pvt Ltd</p>
    
    <form action="/login" method="POST" onsubmit="return handleLogin()">
      <div class="form-group">
        <label for="username">Username:</label>
        <input type="text" id="username" name="username" required>
      </div>
      <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" name="password" required>
      </div>
      <button type="submit" class="login-btn">Login</button>
    </form>
    <script>
    function handleLogin() {
      console.log("Form submitted");
      return true; // allow form submission
    }
    </script>
    
    <div id="session-timer"></div>
  </div>
  )=====";

  html += getGlobalScripts();
  html += R"=====(
</body>
</html>
)=====";
  return html;
}

String getWeekdayPage(WebServer& server) {
  String html = R"=====(
<!DOCTYPE html>
<html>
<head>
  <title>Weekday Schedule</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .day-btn {
      display: inline-block;
      padding: 10px 20px;
      margin: 5px;
      background-color: #3498db;
      color: white;
      text-decoration: none;
      border-radius: 5px;
    }
    .day-btn:hover { background-color: #2980b9; }
    .back-btn { background-color: #7f8c8d; }
    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    input[type="time"] { width: 100%; }
    input[type="checkbox"] {
      margin-right: 8px;
      transform: scale(1.2);
    }
    label {
      display: inline;
      font-weight: normal;
    }
  </style>
</head>
<body>
  <h1>Weekday Schedule</h1>
  
  <div>
    <a href="/" class="day-btn back-btn">Back to Main</a>
    <a href="/weekday?day=1" class="day-btn">Monday</a>
    <a href="/weekday?day=2" class="day-btn">Tuesday</a>
    <a href="/weekday?day=3" class="day-btn">Wednesday</a>
    <a href="/weekday?day=4" class="day-btn">Thursday</a>
    <a href="/weekday?day=5" class="day-btn">Friday</a>
    <a href="/weekday?day=6" class="day-btn">Saturday</a>
    <a href="/weekday?day=0" class="day-btn">Sunday</a>
  </div>
  <div id="session-timer"></div>
)=====";

  if (server.hasArg("day")) {
    int dayIndex = server.arg("day").toInt();
    const char* days[] = { "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday" };

    html += "<h2>" + String(days[dayIndex]) + " Schedule</h2>";
    html += "<form action='/save/weekday' method='POST'>";
    html += "<input type='hidden' name='day' value='" + String(dayIndex) + "'>";
    html += "<label>Siren Duration (seconds): </label>";
    html += "<input type='number' name='duration' value='" + String(weekSchedule[dayIndex].defaultDuration / 1000.0, 1) + "' min='0.1' step='0.1' required><br><br>";

    html += R"=====(
    <table>
      <tr>
        <th>Zone</th>
        <th>Start Time</th>
        <th>Enable</th>
      </tr>
)=====";

    for (int i = 0; i < 10; i++) {
      html += "<tr>";
      html += "<td>" + String(i + 1) + "</td>";
      html += "<td><input type='time' name='time" + String(i) + "' value='" + weekSchedule[dayIndex].zones[i].startTime + "' required></td>";
      html += "<td><input type='checkbox' name='enable" + String(i) + "'" + (weekSchedule[dayIndex].zones[i].enabled ? " checked" : "") + "></td>";
      html += "</tr>";
    }

    html += R"=====(
    </table>
    <div style="margin: 15px 0;">
      <input type="checkbox" id="applyToAll" name="applyToAll">
      <label for="applyToAll">Apply this schedule to ALL days (Monday-Sunday)</label>
    </div>
    <input type='submit' value='Save Settings' style='padding: 10px 20px; background-color: #2ecc71; color: white; border: none; border-radius: 4px; cursor: pointer;'>
    </form>
    <script>
    document.querySelector('form').addEventListener('submit', function(e) {
      if (document.getElementById('applyToAll').checked) {
        if (!confirm('WARNING: This will overwrite ALL DAYS (Monday-Sunday). Continue?')) {
          e.preventDefault();
        }
      }
    });
    </script>
)=====";
  }

  html += R"=====(
  <div id="session-timer"></div>
)=====";

  html += getGlobalScripts();
  html += R"=====(
</body>
</html>
)=====";

  return html;
}

String getHolidayPage() {
  String html = R"=====(
<!DOCTYPE html>
<html>
<head>
  <title>Holiday Schedule</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .holiday-btn {
      display: inline-block;
      padding: 10px 20px;
      margin: 5px;
      background-color: #e74c3c;
      color: white;
      text-decoration: none;
      border-radius: 5px;
    }
    .holiday-btn:hover { background-color: #c0392b; }
    .back-btn { background-color: #7f8c8d; }
    .option-btn { background-color: #3498db; }
    .option-btn:hover { background-color: #2980b9; }
  </style>
</head>
<body>
  <h1>Holiday Schedule</h1>
  
  <div>
    <a href="/" class="holiday-btn back-btn">Back to Main</a>
    <a href="/holiday/enter" class="holiday-btn option-btn">Enter Holiday</a>
    <a href="/holiday/schedule" class="holiday-btn option-btn">Schedule Holiday</a>
    <a href="/holiday/delete" class="holiday-btn option-btn">Delete</a>
  </div>

  <div id="session-timer"></div>
  
  )=====";

  html += getGlobalScripts();
  html += R"=====(
</body>
</html>
)=====";
  return html;
}

String getHolidayEnterPage() {
  String html = R"=====(
<!DOCTYPE html>
<html>
<head>
  <title>Enter Holiday</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .back-btn {
      display: inline-block;
      padding: 10px 20px;
      margin-bottom: 20px;
      background-color: #7f8c8d;
      color: white;
      text-decoration: none;
      border-radius: 5px;
    }
    .form-group { margin-bottom: 15px; }
    label { display: block; margin-bottom: 5px; font-weight: bold; }
    input[type="date"] { 
      padding: 8px;
      width: 200px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .submit-btn {
      padding: 10px 20px;
      background-color: #2ecc71;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <a href="/holiday" class="back-btn">Back to Holiday</a>
  <h1>Enter Holiday Date</h1>
  
  <form action="/add/holiday" method="POST">
    <div class="form-group">
      <label for="date">Holiday Date:</label>
      <input type="date" id="date" name="date" required>
    </div>
    
    <input type="submit" value="Add Holiday" class="submit-btn">
  </form>
  
  <h2>Existing Holidays</h2>
  <ul>
)=====";

  for (JsonPair kv : holidays.as<JsonObject>()) {
    html += "<li><a href=\"/holiday/schedule?date=" + String(kv.key().c_str()) + "\">" + String(kv.key().c_str()) + "</a></li>";
  }

  html += R"=====(
  </ul>
  
  <div id="session-timer"></div>
  
  )=====";

  html += getGlobalScripts();
  html += R"=====(
</body>
</html>
)=====";
  return html;
}

String getHolidaySchedulePage() {
  String html = R"=====(
<!DOCTYPE html>
<html>
<head>
  <title>Holiday Schedule</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .back-btn {
      display: inline-block;
      padding: 10px 20px;
      margin-bottom: 20px;
      background-color: #7f8c8d;
      color: white;
      text-decoration: none;
      border-radius: 5px;
    }
    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    input[type="time"] { width: 100%; }
    .submit-btn {
      padding: 10px 20px;
      background-color: #2ecc71;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <a href="/holiday" class="back-btn">Back to Holiday</a>
  <h1>Holiday Schedule for )=====";

  html += selectedHoliday;
  html += R"=====(</h1>
  
  <form action="/save/holiday" method="POST">
    <input type="hidden" name="date" value=")=====";
  html += selectedHoliday;
  html += R"=====(">
    
    <label>Siren Duration (seconds): </label>
    <input type="number" name="duration" value=")=====";

  float duration = 1.0;
  if (holidays.containsKey(selectedHoliday)) {
    duration = holidays[selectedHoliday]["defaultDuration"].as<float>() / 1000.0;
  }
  html += String(duration, 1);

  html += R"=====(" min="0.1" step="0.1" required><br><br>
    
    <table>
      <tr>
        <th>Zone</th>
        <th>Start Time</th>
        <th>Enable</th>
      </tr>
)=====";

  for (int i = 0; i < 5; i++) {
    html += "<tr>";
    html += "<td>" + String(i + 1) + "</td>";

    String time = "08:00";
    if (holidays.containsKey(selectedHoliday) && holidays[selectedHoliday]["zones"].size() > i) {
      time = holidays[selectedHoliday]["zones"][i]["time"].as<String>();
    }
    html += "<td><input type='time' name='time" + String(i) + "' value='" + time + "' required></td>";

    bool enabled = false;
    if (holidays.containsKey(selectedHoliday) && holidays[selectedHoliday]["zones"].size() > i) {
      enabled = holidays[selectedHoliday]["zones"][i]["enabled"].as<bool>();
    }
    html += "<td><input type='checkbox' name='enable" + String(i) + "'" + (enabled ? " checked" : "") + "></td>";
    html += "</tr>";
  }

  html += R"=====(
    </table>
    <input type="submit" value="Save Settings" class="submit-btn">
  </form>
  
  <div id="session-timer"></div>
  
  )=====";

  html += getGlobalScripts();
  html += R"=====(
</body>
</html>
)=====";
  return html;
}

String getHolidayDeletePage() {
  String html = R"=====(
<!DOCTYPE html>
<html>
<head>
  <title>Delete Holidays</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .back-btn {
      display: inline-block;
      padding: 10px 20px;
      margin-bottom: 20px;
      background-color: #7f8c8d;
      color: white;
      text-decoration: none;
      border-radius: 5px;
    }
    .holiday-item {
      padding: 10px;
      border-bottom: 1px solid #eee;
      display: flex;
      align-items: center;
    }
    .holiday-date {
      flex-grow: 1;
    }
    .delete-btn {
      padding: 10px 20px;
      background-color: #e74c3c;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    .submit-btn {
      margin-top: 20px;
      padding: 10px 20px;
      background-color: #2ecc71;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <a href="/holiday" class="back-btn">Back to Holiday</a>
  <h1>Delete Holidays</h1>
  
  <form action="/delete/holidays" method="POST">
)=====";

  if (holidays.size() > 0) {
    for (JsonPair kv : holidays.as<JsonObject>()) {
      html += "<div class='holiday-item'>";
      html += "<span class='holiday-date'>" + String(kv.key().c_str()) + "</span>";
      html += "<input type='checkbox' name='holiday_" + String(kv.key().c_str()) + "' style='margin-left: 20px;'>";
      html += "</div>";
    }
    html += "<input type='submit' value='Delete Selected' class='submit-btn'>";
  } else {
    html += "<p>No holidays registered</p>";
  }

  html += R"=====(
  </form>
  
  <div id="session-timer"></div>
  
  )=====";

  html += getGlobalScripts();
  html += R"=====(
</body>
</html>
)=====";
  return html;
}

String getRTCPage() {
  DateTime now = rtc.now();
  String currentDate = formatDate(now.year(), now.month(), now.day());
  String currentTime = formatTime(now.hour(), now.minute());

  String html = R"=====(
<!DOCTYPE html>
<html>
<head>
  <title>RTC Settings</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .back-btn {
      display: inline-block;
      padding: 10px 20px;
      margin-bottom: 20px;
      background-color: #7f8c8d;
      color: white;
      text-decoration: none;
      border-radius: 5px;
    }
    .form-group { margin-bottom: 15px; }
    label { display: block; margin-bottom: 5px; font-weight: bold; }
    input[type="date"], input[type="time"], input[type="text"] { 
      padding: 8px;
      width: 200px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .submit-btn {
      padding: 10px 20px;
      background-color: #2ecc71;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    .current-time {
      margin-top: 20px;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 4px;
    }
    .ntp-section {
      margin-top: 30px;
      padding: 15px;
      background-color: #e8f4f8;
      border-radius: 4px;
    }
    .switch {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 34px;
    }
    .switch input { 
      opacity: 0;
      width: 0;
      height: 0;
    }
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
    }
    .slider:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
    }
    input:checked + .slider {
      background-color: #2196F3;
    }
    input:checked + .slider:before {
      transform: translateX(26px);
    }
    .slider.round {
      border-radius: 34px;
    }
    .slider.round:before {
      border-radius: 50%;
    }
  </style>
</head>
<body>
  <a href="/" class="back-btn">Back to Main</a>
  <h1>RTC Date/Time Settings</h1>
  
  <div class="current-time">
    <p><strong>Current RTC Time:</strong> )=====";
  html += currentDate + " " + currentTime;
  html += R"=====(</p>
  </div>
  
  <form action="/setdatetime" method="POST">
    <div class="form-group">
      <label for="date">Date:</label>
      <input type="date" id="date" name="date" value=")=====";
  html += currentDate;
  html += R"=====(" required>
    </div>
    
    <div class="form-group">
      <label for="time">Time:</label>
      <input type="time" id="time" name="time" value=")=====";
  html += currentTime;
  html += R"=====(" required>
    </div>
    
    <input type="submit" value="Set Date/Time" class="submit-btn">
  </form>
  
  <div class="ntp-section">
    <h2>NTP Time Synchronization</h2>
    <form action="/setntp" method="POST">
      <div class="form-group">
        <label for="ntpServer">NTP Server:</label>
        <input type="text" id="ntpServer" name="ntpServer" value=")=====";
  html += ntpServer;
  html += R"=====(" required>
      </div>
      
      <div class="form-group">
        <label>Enable NTP Sync:</label>
        <label class="switch">
          <input type="checkbox" name="ntpEnabled" )=====";
  html += ntpEnabled ? "checked" : "";
  html += R"=====(>
          <span class="slider round"></span>
        </label>
      </div>

      <input type="submit" value="Save NTP Settings" class="submit-btn">
    </form>
    
    <form action="/updatentpnow" method="POST" style="margin-top: 15px;">
      <input type="submit" value="Update from NTP Now" class="submit-btn">
    </form>
  </div>
  <div id="session-timer"></div>
)=====";
  html += getGlobalScripts();
  html += R"=====(
</body>
</html>
)=====";
  return html;
}

String getWiFiSettingsPage() {
  String html = R"=====(
<!DOCTYPE html>
<html>
<head>
  <title>WiFi Settings</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .back-btn {
      display: inline-block;
      padding: 10px 20px;
      margin-bottom: 20px;
      background-color: #7f8c8d;
      color: white;
      text-decoration: none;
      border-radius: 5px;
    }
    .form-group { margin-bottom: 15px; }
    label { display: block; margin-bottom: 5px; font-weight: bold; }
    input[type="text"], input[type="password"] { 
      padding: 8px;
      width: 250px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .submit-btn {
      padding: 10px 20px;
      background-color: #2ecc71;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    .wifi-status {
      margin-top: 20px;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 4px;
    }
    .switch {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 34px;
    }
    .switch input { 
      opacity: 0;
      width: 0;
      height: 0;
    }
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
    }
    .slider:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
    }
    input:checked + .slider {
      background-color: #2196F3;
    }
    input:checked + .slider:before {
      transform: translateX(26px);
    }
    .slider.round {
      border-radius: 34px;
    }
    .slider.round:before {
      border-radius: 50%;
    }
  </style>
</head>
<body>
  <a href="/" class="back-btn">Back to Main</a>
  <h1>WiFi Settings</h1>
  
  <div class="wifi-status">
    <p><strong>Current Mode:</strong> )=====";

  if (WiFi.getMode() == WIFI_AP) {
    html += "AP Mode (SSID: " + String(ap_ssid) + ")";
  } else {
    html += "STA Mode (Connected to: " + String(sta_ssid) + ")";
  }

  html += R"=====(</p>
    <p><strong>IP Address:</strong> )=====";

  if (WiFi.getMode() == WIFI_AP) {
    html += WiFi.softAPIP().toString();
  } else {
    html += WiFi.localIP().toString();
  }

  html += R"=====(</p>
  </div>
  
  <form action="/savewifi" method="POST">
    <div class="form-group">
      <label>WiFi Mode:</label>
      <label class="switch">
        <input type="checkbox" name="sta_mode" )=====";
  html += wifi_sta_mode ? "checked" : "";
  html += R"=====(>
        <span class="slider round"></span>
        <span style="margin-left: 10px;">)=====";
  html += wifi_sta_mode ? " " : " ";
  html += R"=====(</span>
      </label>
    </div>
    
    <div id="sta_settings" style="margin-top: 20px; padding: 15px; background-color: #f0f0f0; border-radius: 5px; display: )=====";
  html += wifi_sta_mode ? "block" : "none";
  html += R"=====(;">
      <div class="form-group">
        <label for="sta_ssid">STA SSID:</label>
        <input type="text" id="sta_ssid" name="sta_ssid" value=")=====";
  html += sta_ssid;
  html += R"=====(" required>
      </div>
      
      <div class="form-group">
        <label for="sta_password">STA Password:</label>
        <input type="password" id="sta_password" name="sta_password" value=")=====";
  html += sta_password;
  html += R"=====(">
      </div>
    </div>
    
    <input type="submit" value="Save Settings" class="submit-btn">
  </form>
  
  <script>
    document.querySelector('input[name="sta_mode"]').addEventListener('change', function() {
      document.getElementById('sta_settings').style.display = this.checked ? 'block' : 'none';
    });
  </script>
</body>
</html>
)=====";
  return html;
}

String updateHTML() {
  return R"=====(
<!DOCTYPE html>
<html>
<head>
  <title>Firmware Update</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body { font-family: Arial, sans-serif; text-align: center; margin: 20px; }
    .btn {
      display: inline-block;
      padding: 10px 20px;
      margin: 10px;
      background-color: #3498db;
      color: white;
      text-decoration: none;
      border-radius: 5px;
    }
    .progress {
      margin: 20px;
      width: 100%;
      background-color: #f3f3f3;
    }
    .progress-bar {
      height: 30px;
      background-color: #4CAF50;
      text-align: center;
      line-height: 30px;
      color: white;
      width: 0%;
    }
  </style>
</head>
<body>
  <h1>Firmware Update</h1>
  <form method='POST' action='/update' enctype='multipart/form-data'>
    <input type='file' name='update' required>
    <br><br>
    <input class='btn' type='submit' value='Update'>
  </form>
  <div class="progress" id="progress" style="display:none;">
    <div class="progress-bar" id="progress-bar">0%</div>
  </div>
  <script>
    document.querySelector('form').addEventListener('submit', function() {
      document.getElementById('progress').style.display = 'block';
    });
  </script>
</body>
</html>
)=====";
}

String getGlobalScripts() {
  return R"=====(
<script>
  var timeLeft = 60;
  var timerInterval;
  var warningShown = false;
  
  function updateTimerDisplay() {
    var timerElement = document.getElementById('session-timer');
    if (timerElement) {
      var minutes = Math.floor(timeLeft / 60);
      var seconds = timeLeft % 60;
      timerElement.innerHTML = 'Auto-logout in: ' + seconds + 's';
      
      if (timeLeft <= 30 && !warningShown) {
        timerElement.style.color = 'red';
        timerElement.style.fontWeight = 'bold';
        warningShown = true;
      }
    }
  }
  
  function resetTimer() {
    timeLeft = 60;
    warningShown = false;
    updateTimerDisplay();
  }
  
  function checkTimeout() {
    updateTimerDisplay();
    if (timeLeft <= 0) {
      clearInterval(timerInterval);
      document.cookie = "ESPSESSIONID=0; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
      window.location.href = "/";
    } else {
      timeLeft--;
    }
  }
  
  function startTimer() {
    clearInterval(timerInterval);
    resetTimer();
    timerInterval = setInterval(checkTimeout, 1000);
    
    document.addEventListener('click', resetTimer);
    document.addEventListener('keypress', resetTimer);
    document.addEventListener('mousemove', resetTimer);
    document.addEventListener('scroll', resetTimer);
    document.addEventListener('touchstart', resetTimer);
  }
  
  window.addEventListener('load', startTimer);
</script>
)=====";
}

String getGlobalStyles() {
  return R"=====(
<style>
  :root {
    --primary-color: #007bff;
    --gradient-start: #74ebd5;
    --gradient-end: #ACB6E5;
  }
  
  body { 
    font-family: 'Arial', sans-serif;
    text-align: center;
    background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
    height: 100vh;
    margin: 0;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #333;
  }
  
  .container {
    background: white;
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    max-width: 500px;
    width: 90%;
    margin: 20px auto;
  }
  
  h1 {
    color: #3500ff;
    margin-top: 0;
  }
  
  button, .btn {
    padding: 12px 30px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 30px;
    font-size: 16px;
    cursor: pointer;
    margin: 10px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
  }
  
  button:hover, .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.2);
    opacity: 0.9;
  }
  
  #session-timer {
    position: fixed;
    bottom: 10px;
    right: 10px;
    background: rgba(255,255,255,0.8);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 14px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
  }
  #session-timer {
    position: fixed;
    bottom: 10px;
    right: 10px;
    background: rgba(255,255,255,0.9);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    z-index: 1000;
    transition: all 0.3s ease;
  }
  
  #session-timer.warning {
    background: rgba(255,200,200,0.9);
    color: #d00;
    font-weight: bold;
  }
</style>
)=====";
}

// void setupWatchdog() {
//   esp_task_wdt_config_t twdt_config = {
//     .timeout_ms = 10000,         // 10 second timeout
//     .idle_core_mask = (1 << 0),  // Watch CPU 0 idle task
//     .trigger_panic = true        // Trigger panic on timeout
//   };
//   ESP_ERROR_CHECK(esp_task_wdt_init(&twdt_config));
//   ESP_ERROR_CHECK(esp_task_wdt_add(NULL));  // Add main loop
// }

// Authentication System
bool processRequest(WebServer& server, bool checkAuth) {
  lastActivityTime = millis();

  if (!checkAuth || !auth_enabled) return true;

  if (!checkAuthentication(server)) {
    server.sendHeader("Location", "/");
    server.send(301);
    return false;
  }
  return true;
}

void protectedRoute(WebServer& server, void (*handler)(WebServer&), bool checkAuth) {
  if (!processRequest(server, checkAuth)) {
    return;
  }
  handler(server);
}

bool checkAuthentication(WebServer& server) {
  if (!auth_enabled) return true;

  // Check session timeout
  if (millis() - lastActivityTime > SESSION_TIMEOUT) {
    currentSessionId = "";
    return false;
  }

  // Check cookie
  if (server.hasHeader("Cookie")) {
    String cookie = server.header("Cookie");
    int sessionIndex = cookie.indexOf("ESPSESSIONID=");

    if (sessionIndex != -1) {
      int endIndex = cookie.indexOf(";", sessionIndex);
      String sessionValue = endIndex == -1 ? cookie.substring(sessionIndex + 13) : cookie.substring(sessionIndex + 13, endIndex);

      if (sessionValue == currentSessionId) {
        lastActivityTime = millis();  // Refresh activity time
        return true;
      }
    }
  }
  return false;
}

void handleConfigSave() {
  // Save configuration
  saveConfiguration();

  // Safely clean up tasks
  safeDeleteTask(&webServerTaskHandle);
  safeDeleteTask(&netMonitorTaskHandle);

  // Delay to allow cleanup to complete
  delay(100);

  // Restart network interfaces
  restartNetworkInterfaces();

  // Send response using the appropriate server
  if (configServerStarted) {
    configServer.send(200, "text/html",
                      "<script>alert('Settings saved!');window.location='/';</script>");
  } else if (runtimeServerStarted) {
    runtimeServer.send(200, "text/html",
                       "<script>alert('Settings saved!');window.location='/';</script>");
  }
}

void checkForTaskHangs() {
  static unsigned long lastCheck = 0;
  if (millis() - lastCheck > 5000) {  // Check every 5 seconds
    lastCheck = millis();

    // Check web server task
    if (webServerTaskHandle != NULL) {
      UBaseType_t stackRemaining = uxTaskGetStackHighWaterMark(webServerTaskHandle);
      if (stackRemaining < 512) {  // If stack is getting low
        Serial.println("WebServer task stack low, restarting...");
        ESP.restart();
      }
    }
  }
}
// ==================== SETUP AND LOOP ====================


// void setup() {
//     Serial.begin(115200);
//     while (!Serial && millis() < 3000) delay(10);

//     // Initialize watchdog first with longer timeout
//     if (!initWatchdog()) {
//         Serial.println("Continuing without watchdog");
//     }
//     esp_task_wdt_add(NULL);  // Add main loop to watchdog

//     // Initialize systems
//     if (!SPIFFS.begin(true)) {
//         Serial.println("SPIFFS Mount Failed");
//         while (1) delay(1000);
//     }

//     preferences.begin("network-config", false);
//     loadConfiguration();
//     loadSettings();
//     loadWiFiSettings();

//     // Initialize hardware
//     tft.init();
//     tft.setRotation(1);
//     tft.fillScreen(TFT_BLACK);

//     Wire.begin();
//     delay(100); // Short delay for I2C to stabilize

//     // Initialize PCF8574
//     if (!pcf.begin()) {
//         Serial.println("PCF8574 initialization failed!");
//         // Show error on display
//         tft.setTextColor(TFT_RED, TFT_BLACK);
//         tft.drawString("PCF8574 Error", 10, 10, 2);
//     }

//     // Initialize RTC with retries
//     int rtcAttempts = 0;
//     while (!rtc.begin() && rtcAttempts < 3) {
//         Serial.println("Could not find RTC module, retrying...");
//         delay(500);
//         rtcAttempts++;
//     }

//     if (rtcAttempts < 3) {
//         rtcInitialized = true;
//         Serial.println("RTC initialized successfully");

//         if (rtc.lostPower()) {
//             Serial.println("RTC lost power, setting default time");
//             rtc.adjust(DateTime(F(__DATE__), F(__TIME__)));
//         }
//     } else {
//         Serial.println("RTC initialization failed!");
//         tft.setTextColor(TFT_RED, TFT_BLACK);
//         tft.drawString("RTC Error", 10, 30, 2);
//     }

//     // Create web server task with proper parameters
//     xTaskCreatePinnedToCore(
//         webServerTask,        // Task function
//         "WebServer",          // Task name
//         12288,                // Increased stack size (12KB)
//         NULL,                 // Parameters
//         2,                    // Priority (higher than default)
//         &webServerTaskHandle, // Task handle
//         1                     // Core 1
//     );

//     // Create network monitor task
//     xTaskCreatePinnedToCore(
//         networkMonitorTask,    // Task function
//         "NetMonitor",         // Task name
//         4096,                 // Stack size
//         NULL,                 // Parameters
//         1,                    // Priority (normal)
//         &netMonitorTaskHandle,// Task handle
//         0                     // Core 0
//     );

//     // Start in AP config mode
//     startConfigMode();

//     // Show AP mode on display
//     tft.fillScreen(TFT_BLACK);
//     tft.setTextColor(TFT_WHITE, TFT_BLACK);
//     tft.drawString("Config Mode", 10, 10, 4);
//     tft.drawString("SSID: " + String(ap_ssid), 10, 50, 2);
//     tft.drawString("IP: ***********", 10, 80, 2);
// }
void setup() {
  Serial.begin(115200);
  while (!Serial && millis() < 3000) delay(10);

  // Initialize Preferences
  preferences.begin("network-config", false);
  loadConfiguration();
  loadSettings();
  loadWiFiSettings();

  // Initialize SPIFFS
  if (!SPIFFS.begin(true)) {
    Serial.println("SPIFFS Mount Failed");
    while (1) delay(1000);
  }

  // Initialize Ethernet if enabled
  if (config.ethernetEnabled) {
    startEthernet();
  }

  // Initialize WiFi if enabled
  if (config.wifiEnabled) {
    startWiFi();
  }

  // Fallback to AP mode if no connections
  if (!eth_connected && !wifi_sta_connected) {
    Serial.println("No active connections, starting AP mode...");
    startConfigMode();
  }

  if (webServerTaskHandle == NULL) {
    if (xTaskCreatePinnedToCore(
          webServerTask,
          "WebServer",
          8192,
          NULL,
          1,
          &webServerTaskHandle,
          1)
        == pdPASS) {
      //esp_task_wdt_add(webServerTaskHandle);  // Add to watchdog after successful creation
    } else {
      Serial.println("Failed to create web server task");
      webServerTaskHandle = NULL;  // Ensure the handle is NULL on failure
    }
  }

  // Create network monitor task only if it hasn't been created yet
  if (netMonitorTaskHandle == NULL) {
    if (xTaskCreatePinnedToCore(
          networkMonitorTask,
          "NetMonitor",
          4096,
          NULL,
          1,
          &netMonitorTaskHandle,
          0)
        == pdPASS) {
      //esp_task_wdt_add(netMonitorTaskHandle);  // Add to watchdog after successful creation
    } else {
      Serial.println("Failed to create network monitor task");
      netMonitorTaskHandle = NULL;  // Ensure the handle is NULL on failure
    }
  }

  // Show initial display state
  //updateDisplay();
}

void loop() {
  // Handle config mode timeout
  if (configModeTimeout) {
    configModeTimeout = false;  // Reset the flag

    Serial.println("Configuration timeout - checking for saved config");
    if (preferences.getBool("configured", false)) {
      startRuntimeMode();
    } else {
      // Extend config mode
      xTimerReset(apModeTimer, 0);
      Serial.println("No config saved - extending AP mode");
    }
  }

  // Your other loop code...
  vTaskDelay(pdMS_TO_TICKS(10));
}
