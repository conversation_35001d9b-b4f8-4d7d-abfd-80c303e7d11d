#ifndef MAIN_H
#define MAIN_H

#include <Arduino.h>
#include <WiFi.h>
#include <WebServer.h>
#include <ETH.h>
#include <HTTPClient.h>
#include <Preferences.h>
#include <Wire.h>
#include <PCF8574.h>
#include <RTClib.h>
#include <ESPmDNS.h>
#include <Update.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>
#include <TFT_eSPI.h>
#include <NTPClient.h>
#include <WiFiUdp.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/timers.h>
//#include <esp_task_wdt.h>

#define CONFIG_FILE "/config.json"
#define CONFIG_SERVER_PORT 80
#define RUNTIME_SERVER_PORT 80
#define CONFIG_MODE_TIMEOUT_MS (2 * 60 * 1000) // 3 minutes
// ETH PHY Configuration (for ESP32 built-in Ethernet)
#define ETH_PHY_TYPE     ETH_PHY_W5500
#define ETH_PHY_ADDR     1
#define ETH_PHY_CS       5
#define ETH_PHY_IRQ      -1
#define ETH_PHY_RST      -1
#define ETH_PHY_SPI_HOST SPI2_HOST
#define ETH_PHY_SPI_SCK  18
#define ETH_PHY_SPI_MISO 19
#define ETH_PHY_SPI_MOSI 23
// Network Configuration Structure
struct NetworkConfig {
    bool wifiEnabled;
    char wifiSSID[32];
    char wifiPassword[64];
    
    bool ethernetEnabled;
    bool useDHCP;
    IPAddress staticIP;
    IPAddress subnet;
    IPAddress gateway;
};

// Alarm Schedule Structures
struct TimeZone {
    uint8_t id;
    String startTime;
    bool enabled;
    uint32_t duration;
};

struct DaySchedule {
    String dayName;
    TimeZone zones[10];
    uint32_t defaultDuration;
};

// Global Variables
extern WebServer wifiServer;
extern WebServer ethernetServer;
extern Preferences preferences;
extern TimerHandle_t apModeTimer;
extern bool eth_connected;
extern NetworkConfig config;
extern bool wifiServerStarted;
extern bool ethernetServerStarted;
extern bool firstBootAfterPowerOn;
extern TaskHandle_t webServerTaskHandle;
extern TaskHandle_t netMonitorTaskHandle;
extern uint8_t eth_mac[6];

// BellBot Project Variables
extern TFT_eSPI tft;
extern PCF8574 pcf;
extern RTC_DS3231 rtc;
extern WiFiUDP ntpUDP;
extern NTPClient timeClient;
extern String ntpServer;
extern unsigned long lastNtpUpdate;
extern const unsigned long ntpUpdateInterval;
extern bool ntpEnabled;
extern bool alarmActive;
extern unsigned long alarmStartTime;
extern unsigned long alarmDuration;
extern String lastTriggeredAlarmTime;
extern bool displayBlinkState;
extern unsigned long lastBlinkTime;
extern const unsigned long blinkInterval;
extern unsigned long lastActivityTime;
extern const unsigned long SESSION_TIMEOUT;
extern String currentSessionId;
extern DaySchedule weekSchedule[7];
extern DynamicJsonDocument holidays;
extern String currentView;
extern String selectedDay;
extern String selectedHoliday;
extern bool otaUpdating;
extern unsigned long otaStartTime;

// WiFi Settings
extern const char* ap_ssid;
extern const char* ap_password;
extern char sta_ssid[32];
extern char sta_password[64];
extern bool wifi_sta_mode;
extern IPAddress local_ip;
extern IPAddress gateway;
extern IPAddress subnet;

// Authentication
extern const char* auth_username;
extern const char* auth_password;
extern bool auth_enabled;
void configModeTimeoutCallback(TimerHandle_t xTimer);
// Function Declarations
// Network Functions
void loadConfiguration();
void saveConfiguration();
void startConfigMode();
void setDefaultConfiguration();
bool shouldStartInConfigMode();
void startRuntimeMode();
void printNetworkSettings();
void startEthernet();
void startWiFi();
void restartWiFi();
void restartEthernet();
void restartNetworkInterfaces();
bool validateNetworkConfig();
bool testServerConnection();

bool isAuthenticated = false; // Define this globally

// Web Server Functions
void setupConfigWebServer();
bool setupRuntimeWebServer();
void webServerTask(void *pvParameters);
void handleEthernetConfigRequest(WiFiClient client);

// Task Functions
void networkMonitorTask(void *pvParameters);
void checkNetworkStatus();

// Watchdog Functions
bool initWatchdog();
void setupWatchdog();
void feedWatchdog();
void criticalOperation();

// Utility Functions
void printSystemInfo();
void checkEthernetStatus();

// BellBot Project Functions
void setupWiFi();
void saveWiFiSettings();
void loadWiFiSettings();
void updateDisplay();
String calculateNextAlarm();
void checkAlarms();
void activateAlarm();
void deactivateAlarm();
void updateTimeFromNTP();
String formatTime(int hour, int minute);
String formatDate(int year, int month, int day);
void loadSettings();
void saveSettings();
void initializeDefaultSettings();

// Web Page Handlers
void handleRoot();
void handleLogin();
void handleLogout();
void handleControl();
void handleWeekday();
void handleHoliday();
void handleHolidayEnter();
void handleHolidaySchedule();
void handleHolidayDelete();
void handleSetDateTime();
void handleSaveWeekday();
void handleSaveHoliday();
void handleAddHoliday();
void handleDeleteHolidays();
void handleSaveWiFi();
void handleSetNTP();
void handleUpdateNTPNow();
void handleUpdateGet();
void handleUpdatePost();
void handleUpdateUpload();
void handleWiFiSettings();

// Web Page Generators
String getHomePage();
String getControlPage();
String getWeekdayPage(WebServer& server);
String getHolidayPage();
String getHolidayEnterPage();
String getHolidaySchedulePage();
String getHolidayDeletePage();
String getRTCPage();
String getWiFiSettingsPage();
String updateHTML();
String getGlobalStyles();
String getGlobalScripts();

// Helper Functions
bool processRequest(WebServer& server, bool checkAuth = true);
void protectedRoute(WebServer& server, void (*handler)(WebServer&), bool checkAuth = true);
bool checkAuthentication(WebServer& server);
void clearCookiesIfNeeded();
void updateActivityTime();

#endif // MAIN_H